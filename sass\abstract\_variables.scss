// Medical Theme Colors
$primary-color: #2d529f;
$secondary-color: #008fe2;
$accent-color: #00a8cc;
$gray-color: #f7f7f7;
$gray-light: #f8f9fa;
$gray-medium: #e9ecef;
$gray-dark: #6c757d;
$dark-color: #212529;
$light-color: #ffffff;
$font-color: #777777;
$font-dark: #333333;
$border-color: #dee2e6;

// Typography
$main-font: "Arial", "Helvetica", sans-serif;
$heading-font: "Arial", "Helvetica", sans-serif;

// Font Sizes
$font-size-base: 1rem;
$font-size-sm: 0.875rem;
$font-size-lg: 1.125rem;
$font-size-xl: 1.25rem;

$h1-size: 2.5rem;
$h2-size: 2rem;
$h3-size: 1.75rem;
$h4-size: 1.5rem;
$h5-size: 1.25rem;
$h6-size: 1rem;

// Legacy variables (keeping for compatibility)
$medium-header: 1.7rem;
$small-header: 1.5rem;

// Spacing
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 3rem;
$spacing-xxl: 4rem;

// Container
$container-max-width: 1200px;
$container-padding: 15px;

// Breakpoints
$mobile: 576px;
$tablet: 768px;
$desktop: 992px;
$large-desktop: 1200px;

// Transitions
$transition-base: all 0.3s ease;
$transition-fast: all 0.15s ease;

// Border Radius
$border-radius-sm: 0.25rem;
$border-radius: 0.375rem;
$border-radius-lg: 0.5rem;

// Shadows
$shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
$shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
$shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
