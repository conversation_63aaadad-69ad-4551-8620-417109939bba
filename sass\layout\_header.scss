// Header Styles
.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: $light-color;
  box-shadow: $shadow-sm;

  // Top contact bar
  &__top {
    background-color: $primary-color;
    color: $light-color;
    padding: $spacing-sm 0;
    font-size: $font-size-sm;

    @include mobile {
      display: none;
    }
  }

  &__contact {
    @include flex-between;

    @include tablet {
      justify-content: center;
      gap: $spacing-xl;
    }
  }

  .contact-item {
    @include flex-center;
    gap: $spacing-sm;

    i {
      color: $secondary-color;
    }
  }

  // Main navigation
  &__nav {
    padding: $spacing-md 0;
  }

  .nav-wrapper {
    @include flex-between;
    align-items: center;
  }

  .logo {
    img {
      height: 50px;
      width: auto;
    }
  }

  .nav-menu {
    @include flex-center;
    gap: $spacing-lg;

    @include mobile {
      display: none;
    }

    li {
      position: relative;
    }
  }

  .nav-link {
    color: $font-dark;
    font-weight: 600;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius;
    transition: $transition-base;
    @include flex-center;
    gap: $spacing-xs;

    &:hover,
    &.active {
      color: $primary-color;
      background-color: rgba($primary-color, 0.1);
    }
  }

  // Dropdown menu
  .dropdown {
    &:hover .dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: $light-color;
    box-shadow: $shadow;
    border-radius: $border-radius;
    padding: $spacing-sm 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: $transition-base;

    li {
      width: 100%;
    }

    a {
      display: block;
      padding: $spacing-sm $spacing-md;
      color: $font-dark;
      transition: $transition-fast;

      &:hover {
        background-color: $gray-light;
        color: $primary-color;
      }
    }
  }

  // Mobile menu toggle
  .mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;

    @include mobile {
      display: flex;
    }

    span {
      width: 25px;
      height: 3px;
      background-color: $font-dark;
      transition: $transition-base;
    }

    &:hover span {
      background-color: $primary-color;
    }
  }
}
