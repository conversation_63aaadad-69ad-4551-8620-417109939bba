// Header Styles
.header {
  position: relative;
  z-index: 1000;
  background-color: $light-color;

  // Top contact bar
  &__top {
    background-color: $dark-color;
    color: $light-color;
    padding: 8px 0;
    font-size: 13px;

    @include mobile {
      display: none;
    }
  }

  &__contact {
    @include flex-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;

    @include tablet {
      justify-content: center;
      gap: $spacing-xl;
    }
  }

  .contact-item {
    @include flex-center;
    gap: 8px;
    font-weight: 600;

    i {
      color: $primary-color;
      font-size: 14px;
    }
  }

  // Main navigation
  &__nav {
    padding: 15px 0;
    border-bottom: 1px solid $border-color;
  }

  .nav-wrapper {
    @include flex-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
  }

  .logo {
    img {
      height: 45px;
      width: auto;
    }
  }

  .nav-menu {
    @include flex-center;
    gap: 0;

    @include mobile {
      display: none;
    }

    li {
      position: relative;
    }
  }

  .nav-link {
    color: $font-dark;
    font-weight: 600;
    padding: 15px 20px;
    transition: $transition-base;
    @include flex-center;
    gap: 5px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &:hover,
    &.active {
      color: $primary-color;
    }
  }

  // Dropdown menu
  .dropdown {
    &:hover .dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: $light-color;
    box-shadow: $shadow;
    border-radius: $border-radius;
    padding: $spacing-sm 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: $transition-base;

    li {
      width: 100%;
    }

    a {
      display: block;
      padding: $spacing-sm $spacing-md;
      color: $font-dark;
      transition: $transition-fast;

      &:hover {
        background-color: $gray-light;
        color: $primary-color;
      }
    }
  }

  // Mobile menu toggle
  .mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;

    @include mobile {
      display: flex;
    }

    span {
      width: 25px;
      height: 3px;
      background-color: $font-dark;
      transition: $transition-base;
    }

    &:hover span {
      background-color: $primary-color;
    }
  }
}
