// Legacy mixin (keeping for compatibility)
@mixin hover-effect($bgcolor, $color: $light-color) {
  color: $color;
  background-color: $bgcolor;
}

// Responsive Breakpoints
@mixin mobile {
  @media (max-width: #{$mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$tablet}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$desktop}) {
    @content;
  }
}

@mixin large-desktop {
  @media (min-width: #{$large-desktop}) {
    @content;
  }
}

// Flexbox utilities
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// Button mixin
@mixin button-style(
  $bg-color,
  $text-color: $light-color,
  $hover-bg: darken($bg-color, 10%)
) {
  background-color: $bg-color;
  color: $text-color;
  border: none;
  padding: 12px 30px;
  border-radius: $border-radius;
  text-decoration: none;
  display: inline-block;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: $transition-base;
  cursor: pointer;

  &:hover {
    background-color: $hover-bg;
    transform: translateY(-2px);
    box-shadow: $shadow;
  }

  &:active {
    transform: translateY(0);
  }
}

// Card shadow
@mixin card-shadow {
  box-shadow: $shadow;
  transition: $transition-base;

  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-5px);
  }
}

// Section padding
@mixin section-padding {
  padding: $spacing-xxl 0;

  @include mobile {
    padding: $spacing-xl 0;
  }
}

// Container
@mixin container {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $container-padding;
}

// Background image
@mixin bg-image {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

// Overlay
@mixin overlay($opacity: 0.5) {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba($dark-color, $opacity);
    z-index: 1;
  }
}
