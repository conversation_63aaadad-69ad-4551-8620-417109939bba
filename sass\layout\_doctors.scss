// Doctors Section
.doctors {
  @include section-padding;
  background-color: $light-color;

  &__grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: $spacing-lg;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
    }

    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }

    @include desktop {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

// Doctor card component
.doctor-card {
  background-color: $light-color;
  border-radius: $border-radius-lg;
  overflow: hidden;
  @include card-shadow;
  transition: $transition-base;
  position: relative;

  &:hover {
    transform: translateY(-5px);
  }

  &__image {
    position: relative;
    overflow: hidden;
    height: 250px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: $transition-base;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  &__content {
    padding: $spacing-lg $spacing-md;
    text-align: center;
    position: relative;
  }

  &__specialty {
    display: inline-block;
    background-color: $primary-color;
    color: $light-color;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius;
    font-size: $font-size-sm;
    font-weight: 500;
    text-transform: capitalize;
    margin-bottom: $spacing-sm;
    text-decoration: none;
    transition: $transition-fast;

    &:hover {
      background-color: $secondary-color;
    }
  }

  &__name {
    color: $font-dark;
    font-size: $font-size-lg;
    margin-bottom: $spacing-md;
    font-weight: 600;
  }

  &__link {
    position: absolute;
    bottom: $spacing-md;
    right: $spacing-md;
    width: 35px;
    height: 35px;
    background-color: $light-color;
    color: $primary-color;
    border: 2px solid $primary-color;
    border-radius: 50%;
    @include flex-center;
    transition: $transition-base;
    text-decoration: none;

    &:hover {
      background-color: $primary-color;
      color: $light-color;
      transform: scale(1.1);
    }

    i {
      font-size: $font-size-sm;
    }
  }
}
