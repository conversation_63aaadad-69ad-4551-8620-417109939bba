// Doctors Section
.doctors {
  @include section-padding;
  background-color: $light-color;

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-xl;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
    }
  }
}

// Doctor card component
.doctor-card {
  background-color: $light-color;
  border-radius: $border-radius-lg;
  overflow: hidden;
  @include card-shadow;
  transition: $transition-base;

  &:hover {
    transform: translateY(-10px);
  }

  &__image {
    position: relative;
    overflow: hidden;
    height: 300px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: $transition-base;
    }

    &:hover img {
      transform: scale(1.1);
    }
  }

  &__content {
    padding: $spacing-lg;
    text-align: center;
    position: relative;
  }

  &__specialty {
    display: inline-block;
    background-color: $primary-color;
    color: $light-color;
    padding: $spacing-xs $spacing-md;
    border-radius: $border-radius;
    font-size: $font-size-sm;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: $spacing-md;
  }

  &__name {
    color: $font-dark;
    font-size: $h5-size;
    margin-bottom: $spacing-lg;
    font-weight: 600;
  }

  &__link {
    position: absolute;
    bottom: $spacing-lg;
    right: $spacing-lg;
    width: 40px;
    height: 40px;
    background-color: $secondary-color;
    color: $light-color;
    border-radius: 50%;
    @include flex-center;
    transition: $transition-base;
    text-decoration: none;

    &:hover {
      background-color: $primary-color;
      transform: scale(1.1);
    }

    i {
      font-size: $font-size-sm;
    }
  }
}
