// CSS Reset
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: $main-font;
  font-size: $font-size-base;
  line-height: 1.6;
  color: $font-color;
  background-color: $light-color;
}

// Container
.container {
  @include container;
}

// Remove default list styles
ul,
ol {
  list-style: none;
}

// Remove default link styles
a {
  text-decoration: none;
  color: inherit;
}

// Image responsive
img {
  max-width: 100%;
  height: auto;
  display: block;
}

// Button reset
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

// Form elements
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
}

// Remove outline on focus for better accessibility
:focus {
  outline: 2px solid $primary-color;
  outline-offset: 2px;
}
