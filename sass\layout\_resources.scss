// Resources Section
.resources {
  @include section-padding;
  background-color: $gray-color;

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: $spacing-xl;
    margin-bottom: $spacing-xl;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
    }
  }

  &__btn {
    @include flex-center;
  }
}

// Resource card component
.resource-card {
  background-color: $light-color;
  border-radius: $border-radius-lg;
  overflow: hidden;
  @include card-shadow;
  transition: $transition-base;

  &:hover {
    transform: translateY(-5px);
  }

  &__image {
    position: relative;
    overflow: hidden;
    height: 200px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: $transition-base;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  &__content {
    padding: $spacing-lg;

    h3 {
      color: $font-dark;
      font-size: $h5-size;
      margin-bottom: $spacing-md;
      font-weight: 600;
    }

    p {
      color: $font-color;
      line-height: 1.6;
      margin-bottom: 0;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}
