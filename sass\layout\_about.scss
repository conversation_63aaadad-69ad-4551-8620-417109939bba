// About Section
.about {
  @include section-padding;
  background-color: $light-color;

  &__wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-xxl;
    align-items: center;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-xl;
    }
  }

  &__content {
    .sec-title {
      text-align: left;
      margin-bottom: $spacing-lg;

      &__header {
        &::after {
          left: 0;
          transform: none;
        }
      }
    }
  }

  &__text {
    p {
      font-size: $font-size-lg;
      line-height: 1.8;
      color: $font-color;
      margin-bottom: $spacing-lg;
    }
  }

  &__image {
    position: relative;
    border-radius: $border-radius-lg;
    overflow: hidden;
    box-shadow: $shadow;

    img {
      width: 100%;
      height: 400px;
      object-fit: cover;
      transition: $transition-base;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }
}
