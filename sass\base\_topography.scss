// Typography
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: $heading-font;
  font-weight: 700;
  line-height: 1.2;
  color: $font-dark;
  margin-bottom: $spacing-md;
}

h1 {
  font-size: $h1-size;

  @include mobile {
    font-size: $h2-size;
  }
}

h2 {
  font-size: $h2-size;

  @include mobile {
    font-size: $h3-size;
  }
}

h3 {
  font-size: $h3-size;

  @include mobile {
    font-size: $h4-size;
  }
}

h4 {
  font-size: $h4-size;
}

h5 {
  font-size: $h5-size;
}

h6 {
  font-size: $h6-size;
}

p {
  margin-bottom: $spacing-md;
  line-height: 1.7;
}

// Links
a {
  color: $primary-color;
  transition: $transition-fast;

  &:hover {
    color: $secondary-color;
  }
}

// Section titles
.sec-title {
  text-align: center;
  margin-bottom: $spacing-xl;

  &__header {
    font-size: $h2-size;
    color: $font-dark;
    text-transform: capitalize;
    margin-bottom: $spacing-sm;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background-color: $primary-color;
    }
  }

  &__subtitle {
    color: $font-color;
    font-size: $font-size-lg;
    margin-bottom: 0;
  }
}

// Text utilities
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: $primary-color;
}

.text-secondary {
  color: $secondary-color;
}

.text-muted {
  color: $font-color;
}

.text-dark {
  color: $font-dark;
}

.text-light {
  color: $light-color;
}
