// Footer Section
.footer {
  background-color: $dark-color;
  color: $light-color;
  padding: $spacing-xxl 0 $spacing-lg;

  &__content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-xl;
    margin-bottom: $spacing-xl;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
      text-align: center;
    }
  }

  &__section {
    h3 {
      color: $light-color;
      font-size: $h5-size;
      margin-bottom: $spacing-lg;
      font-weight: 600;
    }

    address {
      font-style: normal;
      line-height: 1.8;
      color: rgba($light-color, 0.8);
    }

    .hours {
      p {
        color: rgba($light-color, 0.8);
        margin-bottom: $spacing-sm;
      }
    }
  }

  .emergency-number {
    color: $secondary-color;
    font-size: $font-size-xl;
    font-weight: 700;
    text-decoration: none;
    transition: $transition-fast;

    &:hover {
      color: $primary-color;
    }
  }

  .social-links {
    @include flex-center;
    gap: $spacing-md;

    @include mobile {
      justify-content: center;
    }

    a {
      width: 40px;
      height: 40px;
      background-color: $primary-color;
      color: $light-color;
      border-radius: 50%;
      @include flex-center;
      transition: $transition-base;
      text-decoration: none;

      &:hover {
        background-color: $secondary-color;
        transform: translateY(-3px);
      }

      i {
        font-size: $font-size-base;
      }
    }
  }

  &__bottom {
    border-top: 1px solid rgba($light-color, 0.2);
    padding-top: $spacing-lg;
    text-align: center;

    p {
      color: rgba($light-color, 0.7);
      margin-bottom: 0;
      font-size: $font-size-sm;
    }
  }
}
