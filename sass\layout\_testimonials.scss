// Testimonials Section
.testimonials {
  @include section-padding;
  background-color: $gray-color;
  position: relative;

  &__slider {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
  }
}

// Testimonial component
.testimonial {
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transition: $transition-base;

  &.active {
    opacity: 1;
    visibility: visible;
  }

  blockquote {
    background-color: $light-color;
    padding: $spacing-xxl;
    border-radius: $border-radius-lg;
    box-shadow: $shadow;
    position: relative;
    margin-bottom: $spacing-lg;

    &::before {
      content: '"';
      position: absolute;
      top: -20px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 4rem;
      color: $primary-color;
      font-weight: 700;
      line-height: 1;
    }

    p {
      font-size: $font-size-lg;
      line-height: 1.8;
      color: $font-color;
      font-style: italic;
      margin-bottom: $spacing-lg;
    }

    cite {
      color: $font-dark;
      font-size: $font-size-lg;
      font-weight: 600;
      font-style: normal;

      &::before {
        content: "— ";
        color: $primary-color;
      }
    }
  }
}
