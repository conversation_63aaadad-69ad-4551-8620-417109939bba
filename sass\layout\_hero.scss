// Hero Section
.hero {
  position: relative;
  min-height: 70vh;
  overflow: hidden;

  // Hero slider
  &__slider {
    position: relative;
    height: 70vh;
  }

  &__slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;

    &.active {
      opacity: 1;
    }
  }

  &__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    @include bg-image;
    @include overlay(0.4);
  }

  &__content {
    position: relative;
    z-index: 2;
    height: 100%;
    @include flex-center;
    @include flex-column;
    text-align: center;
    color: $light-color;
    padding: $spacing-xl 0;
  }

  &__title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: $spacing-md;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);

    @include mobile {
      font-size: 2.5rem;
    }
  }

  &__subtitle {
    font-size: $font-size-xl;
    margin-bottom: $spacing-sm;
    font-weight: 600;
  }

  &__text {
    font-size: $font-size-lg;
    opacity: 0.9;
  }

  // Medical info card
  &__info-card {
    position: relative;
    margin-top: -80px;
    z-index: 3;

    @include mobile {
      margin-top: -40px;
    }
  }
}

// Info card component
.info-card {
  background-color: $light-color;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-lg;
  padding: $spacing-xl;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: $spacing-xl;
  align-items: center;

  @include mobile {
    grid-template-columns: 1fr;
    text-align: center;
    padding: $spacing-lg;
  }

  &__icon {
    img {
      width: 80px;
      height: 80px;
      object-fit: contain;
    }
  }

  &__content {
    h3 {
      color: $primary-color;
      font-size: $h3-size;
      margin-bottom: $spacing-md;
      text-align: center;

      @include mobile {
        margin-bottom: $spacing-sm;
      }
    }
  }

  &__details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-lg;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-md;
    }
  }

  .detail-item {
    h4 {
      color: $font-dark;
      font-size: $font-size-lg;
      margin-bottom: $spacing-sm;
      font-weight: 600;
    }

    p {
      color: $font-color;
      line-height: 1.6;
      margin-bottom: 0;
    }
  }

  .emergency {
    text-align: center;
    padding: $spacing-md;
    background-color: $gray-light;
    border-radius: $border-radius;

    h5 {
      color: $font-dark;
      margin-bottom: $spacing-sm;
      font-size: $font-size-base;
    }
  }

  .emergency-phone {
    color: $primary-color;
    font-size: $font-size-xl;
    font-weight: 700;
    text-decoration: none;
    transition: $transition-fast;

    &:hover {
      color: $secondary-color;
    }
  }
}
