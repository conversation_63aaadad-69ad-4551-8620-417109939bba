// Hero Section
.hero {
  position: relative;
  min-height: 70vh;
  overflow: hidden;

  // Hero slider
  &__slider {
    position: relative;
    height: 70vh;
  }

  &__slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;

    &.active {
      opacity: 1;
    }
  }

  &__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    @include bg-image;
    @include overlay(0.4);
  }

  &__content {
    position: relative;
    z-index: 2;
    height: 100%;
    @include flex-center;
    @include flex-column;
    text-align: center;
    color: $light-color;
    padding: $spacing-xl 0;
  }

  &__title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: $spacing-md;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);

    @include mobile {
      font-size: 2.5rem;
    }
  }

  &__subtitle {
    font-size: $font-size-xl;
    margin-bottom: $spacing-sm;
    font-weight: 600;
  }

  &__text {
    font-size: $font-size-lg;
    opacity: 0.9;
  }

  // Medical info card
  &__info-card {
    position: absolute;
    bottom: -100px;
    left: 0;
    right: 0;
    z-index: 3;

    @include mobile {
      position: relative;
      bottom: auto;
      margin-top: -40px;
    }
  }
}

// Info card component
.info-card {
  background-color: $light-color;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-lg;
  padding: $spacing-xl;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: $spacing-xl;
  align-items: center;
  max-width: 900px;
  margin: 0 auto;

  @include mobile {
    grid-template-columns: 1fr;
    text-align: center;
    padding: $spacing-lg;
  }

  &__icon {
    @include flex-center;

    img {
      width: 60px;
      height: 60px;
      object-fit: contain;
    }
  }

  &__content {
    h3 {
      color: $primary-color;
      font-size: $h4-size;
      margin-bottom: $spacing-sm;
      font-weight: 600;

      @include mobile {
        text-align: center;
      }
    }
  }

  &__details {
    display: flex;
    gap: $spacing-xl;

    @include mobile {
      flex-direction: column;
      gap: $spacing-md;
    }
  }

  .detail-item {
    text-align: center;

    h4 {
      color: $font-dark;
      font-size: $font-size-base;
      margin-bottom: $spacing-xs;
      font-weight: 600;
    }

    p {
      color: $font-color;
      line-height: 1.4;
      margin-bottom: 0;
      font-size: $font-size-sm;
    }
  }

  .emergency {
    text-align: center;

    h5 {
      color: $font-dark;
      margin-bottom: $spacing-xs;
      font-size: $font-size-sm;
      font-weight: 600;
    }
  }

  .emergency-phone {
    color: $primary-color;
    font-size: $font-size-lg;
    font-weight: 700;
    text-decoration: none;
    transition: $transition-fast;

    &:hover {
      color: $secondary-color;
    }
  }
}
