// Hero Section
.hero {
  position: relative;
  height: 600px;
  overflow: hidden;

  // Hero slider
  &__slider {
    position: relative;
    height: 100%;
  }

  &__slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;

    &.active {
      opacity: 1;
    }
  }

  &__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    @include bg-image;
    @include overlay(0.3);
  }

  &__content {
    position: relative;
    z-index: 2;
    height: 100%;
    @include flex-center;
    @include flex-column;
    text-align: center;
    color: $light-color;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
  }

  &__title {
    font-size: 48px;
    font-weight: 300;
    margin-bottom: 15px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    line-height: 1.2;

    @include mobile {
      font-size: 32px;
    }
  }

  &__subtitle {
    font-size: 18px;
    margin-bottom: 8px;
    font-weight: 400;
    opacity: 0.95;
  }

  &__text {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 300;
  }

  // Medical info card
  &__info-card {
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    width: 100%;
    max-width: 1200px;
    padding: 0 15px;

    @include mobile {
      position: relative;
      bottom: auto;
      transform: none;
      left: auto;
      margin-top: -30px;
    }
  }
}

// Info card component
.info-card {
  background-color: $light-color;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 30px;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 40px;
  align-items: center;

  @include mobile {
    grid-template-columns: 1fr;
    text-align: center;
    padding: 20px;
    gap: 20px;
  }

  &__icon {
    @include flex-center;

    img {
      width: 50px;
      height: 50px;
      object-fit: contain;
    }
  }

  &__content {
    h3 {
      color: $primary-color;
      font-size: 24px;
      margin-bottom: 0;
      font-weight: 600;
      text-align: center;

      @include mobile {
        margin-bottom: 15px;
      }
    }
  }

  &__details {
    display: flex;
    gap: 30px;

    @include mobile {
      flex-direction: column;
      gap: 15px;
    }
  }

  .detail-item {
    text-align: left;

    h4 {
      color: $font-dark;
      font-size: 14px;
      margin-bottom: 5px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    p {
      color: $font-color;
      line-height: 1.4;
      margin-bottom: 0;
      font-size: 13px;
    }
  }

  .emergency {
    text-align: center;
    background-color: $gray-light;
    padding: 15px;
    border-radius: 3px;

    h5 {
      color: $font-dark;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .emergency-phone {
    color: $primary-color;
    font-size: 18px;
    font-weight: 700;
    text-decoration: none;
    transition: $transition-fast;

    &:hover {
      color: $secondary-color;
    }
  }
}
