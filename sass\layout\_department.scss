// Departments Section
.departments {
  @include section-padding;
  background-color: $gray-color;

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacing-xl;
    margin-bottom: $spacing-xl;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
    }
  }

  &__btn {
    @include flex-center;
  }
}

// Department card icon component
.card-icon {
  background-color: $light-color;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  text-align: center;
  @include card-shadow;
  transition: $transition-base;

  &:hover {
    transform: translateY(-10px);
  }

  &__img {
    margin-bottom: $spacing-lg;

    img {
      width: 80px;
      height: 80px;
      margin: 0 auto;
      object-fit: contain;
      filter: brightness(0) saturate(100%) invert(27%) sepia(51%)
        saturate(2878%) hue-rotate(202deg) brightness(102%) contrast(97%);
      transition: $transition-base;
    }
  }

  &:hover &__img img {
    filter: brightness(0) saturate(100%) invert(47%) sepia(99%) saturate(2174%)
      hue-rotate(184deg) brightness(98%) contrast(98%);
    transform: scale(1.1);
  }

  &__text {
    h3 {
      color: $font-dark;
      font-size: $h4-size;
      margin-bottom: $spacing-md;
      text-transform: capitalize;
      font-weight: 600;
    }

    p {
      color: $font-color;
      line-height: 1.6;
      margin-bottom: 0;
    }
  }
}
