// Departments Section
.departments {
  padding: 80px 0;
  background-color: $gray-color;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 50px;

    @include mobile {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__btn {
    @include flex-center;
  }
}

// Department card icon component
.card-icon {
  background-color: $light-color;
  border-radius: 5px;
  padding: 40px 30px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid $border-color;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &__img {
    margin-bottom: 25px;

    img {
      width: 60px;
      height: 60px;
      margin: 0 auto;
      object-fit: contain;
      transition: $transition-base;
    }
  }

  &:hover &__img img {
    transform: scale(1.1);
  }

  &__text {
    h3 {
      color: $font-dark;
      font-size: 18px;
      margin-bottom: 15px;
      text-transform: capitalize;
      font-weight: 600;

      a {
        color: inherit;
        text-decoration: none;
        transition: $transition-fast;

        &:hover {
          color: $primary-color;
        }
      }
    }

    p {
      color: $font-color;
      line-height: 1.6;
      margin-bottom: 0;
      font-size: 14px;
    }
  }
}
