// Insurance Section
.insurance {
  @include section-padding;
  background-color: $light-color;

  &__logos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: $spacing-xl;
    align-items: center;

    @include mobile {
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-lg;
    }
  }

  &__logo {
    @include flex-center;
    padding: $spacing-lg;
    border-radius: $border-radius;
    transition: $transition-base;

    &:hover {
      background-color: $gray-light;
      transform: translateY(-5px);
    }

    img {
      max-width: 120px;
      max-height: 60px;
      object-fit: contain;
      filter: grayscale(100%);
      transition: $transition-base;
    }

    &:hover img {
      filter: grayscale(0%);
    }
  }
}
