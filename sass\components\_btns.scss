// Button Components
.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: $border-radius;
  text-decoration: none;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: $transition-base;
  cursor: pointer;
  border: 2px solid transparent;
  text-align: center;
  font-size: $font-size-base;

  // Primary button
  &--primary {
    @include button-style($primary-color);
  }

  // Secondary button
  &--secondary {
    @include button-style($secondary-color);
  }

  // Dark button
  &--dark {
    @include button-style($dark-color);
  }

  // Outline buttons
  &--outline-primary {
    background-color: transparent;
    color: $primary-color;
    border-color: $primary-color;

    &:hover {
      background-color: $primary-color;
      color: $light-color;
      transform: translateY(-2px);
      box-shadow: $shadow;
    }
  }

  &--outline-secondary {
    background-color: transparent;
    color: $secondary-color;
    border-color: $secondary-color;

    &:hover {
      background-color: $secondary-color;
      color: $light-color;
      transform: translateY(-2px);
      box-shadow: $shadow;
    }
  }

  // Button sizes
  &--sm {
    padding: 8px 20px;
    font-size: $font-size-sm;
  }

  &--lg {
    padding: 16px 40px;
    font-size: $font-size-lg;
  }

  // Disabled state
  &:disabled,
  &--disabled {
    background-color: $gray-medium;
    color: $gray-dark;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;

    &:hover {
      background-color: $gray-medium;
      color: $gray-dark;
      transform: none;
      box-shadow: none;
    }
  }
}
