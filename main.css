@charset "UTF-8";
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: "Arial", "Helvetica", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #777777;
  background-color: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

ul,
ol {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
}

:focus {
  outline: 2px solid #2d529f;
  outline-offset: 2px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Arial", "Helvetica", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  color: #333333;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
}
@media (max-width: 575px) {
  h1 {
    font-size: 2rem;
  }
}

h2 {
  font-size: 2rem;
}
@media (max-width: 575px) {
  h2 {
    font-size: 1.75rem;
  }
}

h3 {
  font-size: 1.75rem;
}
@media (max-width: 575px) {
  h3 {
    font-size: 1.5rem;
  }
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

p {
  margin-bottom: 1rem;
  line-height: 1.7;
}

a {
  color: #2d529f;
  transition: all 0.15s ease;
}
a:hover {
  color: #008fe2;
}

.sec-title {
  text-align: center;
  margin-bottom: 3rem;
}
.sec-title__header {
  font-size: 2rem;
  color: #333333;
  text-transform: capitalize;
  margin-bottom: 0.5rem;
  position: relative;
}
.sec-title__header::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: #2d529f;
}
.sec-title__subtitle {
  color: #777777;
  font-size: 1.125rem;
  margin-bottom: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: #2d529f;
}

.text-secondary {
  color: #008fe2;
}

.text-muted {
  color: #777777;
}

.text-dark {
  color: #333333;
}

.text-light {
  color: #ffffff;
}

.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: #ffffff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
.header__top {
  background-color: #2d529f;
  color: #ffffff;
  padding: 0.5rem 0;
  font-size: 0.875rem;
}
@media (max-width: 575px) {
  .header__top {
    display: none;
  }
}
.header__contact {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (min-width: 768px) {
  .header__contact {
    justify-content: center;
    gap: 3rem;
  }
}
.header .contact-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}
.header .contact-item i {
  color: #008fe2;
}
.header__nav {
  padding: 1rem 0;
}
.header .nav-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-items: center;
}
.header .logo img {
  height: 50px;
  width: auto;
}
.header .nav-menu {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
}
@media (max-width: 575px) {
  .header .nav-menu {
    display: none;
  }
}
.header .nav-menu li {
  position: relative;
}
.header .nav-link {
  color: #333333;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}
.header .nav-link:hover, .header .nav-link.active {
  color: #2d529f;
  background-color: rgba(45, 82, 159, 0.1);
}
.header .dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.header .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #ffffff;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.375rem;
  padding: 0.5rem 0;
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}
.header .dropdown-menu li {
  width: 100%;
}
.header .dropdown-menu a {
  display: block;
  padding: 0.5rem 1rem;
  color: #333333;
  transition: all 0.15s ease;
}
.header .dropdown-menu a:hover {
  background-color: #f8f9fa;
  color: #2d529f;
}
.header .mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
}
@media (max-width: 575px) {
  .header .mobile-menu-toggle {
    display: flex;
  }
}
.header .mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background-color: #333333;
  transition: all 0.3s ease;
}
.header .mobile-menu-toggle:hover span {
  background-color: #2d529f;
}

.hero {
  position: relative;
  min-height: 70vh;
  overflow: hidden;
}
.hero__slider {
  position: relative;
  height: 70vh;
}
.hero__slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}
.hero__slide.active {
  opacity: 1;
}
.hero__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}
.hero__bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(33, 37, 41, 0.4);
  z-index: 1;
}
.hero__content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  text-align: center;
  color: #ffffff;
  padding: 3rem 0;
}
.hero__title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}
@media (max-width: 575px) {
  .hero__title {
    font-size: 2.5rem;
  }
}
.hero__subtitle {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}
.hero__text {
  font-size: 1.125rem;
  opacity: 0.9;
}
.hero__info-card {
  position: absolute;
  bottom: -100px;
  left: 0;
  right: 0;
  z-index: 3;
}
@media (max-width: 575px) {
  .hero__info-card {
    position: relative;
    bottom: auto;
    margin-top: -40px;
  }
}

.info-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  padding: 3rem;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 3rem;
  align-items: center;
  max-width: 900px;
  margin: 0 auto;
}
@media (max-width: 575px) {
  .info-card {
    grid-template-columns: 1fr;
    text-align: center;
    padding: 1.5rem;
  }
}
.info-card__icon {
  display: flex;
  align-items: center;
  justify-content: center;
}
.info-card__icon img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}
.info-card__content h3 {
  color: #2d529f;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}
@media (max-width: 575px) {
  .info-card__content h3 {
    text-align: center;
  }
}
.info-card__details {
  display: flex;
  gap: 3rem;
}
@media (max-width: 575px) {
  .info-card__details {
    flex-direction: column;
    gap: 1rem;
  }
}
.info-card .detail-item {
  text-align: center;
}
.info-card .detail-item h4 {
  color: #333333;
  font-size: 1rem;
  margin-bottom: 0.25rem;
  font-weight: 600;
}
.info-card .detail-item p {
  color: #777777;
  line-height: 1.4;
  margin-bottom: 0;
  font-size: 0.875rem;
}
.info-card .emergency {
  text-align: center;
}
.info-card .emergency h5 {
  color: #333333;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
}
.info-card .emergency-phone {
  color: #2d529f;
  font-size: 1.125rem;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.15s ease;
}
.info-card .emergency-phone:hover {
  color: #008fe2;
}

.about {
  padding-top: 150px;
  padding-bottom: 4rem;
  background-color: #ffffff;
}
@media (max-width: 575px) {
  .about {
    padding-top: 100px;
    padding-bottom: 3rem;
  }
}
.about__wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}
@media (max-width: 575px) {
  .about__wrapper {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}
.about__content .sec-title {
  text-align: left;
  margin-bottom: 1.5rem;
}
.about__content .sec-title__header::after {
  left: 0;
  transform: none;
}
.about__text p {
  font-size: 1.125rem;
  line-height: 1.8;
  color: #777777;
  margin-bottom: 1.5rem;
}
.about__image {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.about__image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: all 0.3s ease;
}
.about__image:hover img {
  transform: scale(1.05);
}

.departments {
  padding: 4rem 0;
  background-color: #f7f7f7;
}
@media (max-width: 575px) {
  .departments {
    padding: 3rem 0;
  }
}
.departments__grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
  margin-bottom: 3rem;
}
@media (max-width: 575px) {
  .departments__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
@media (min-width: 768px) {
  .departments__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
.departments__btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon {
  background-color: #ffffff;
  border-radius: 0.5rem;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.card-icon:hover {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  transform: translateY(-5px);
}
.card-icon:hover {
  transform: translateY(-10px);
}
.card-icon__img {
  margin-bottom: 1.5rem;
}
.card-icon__img img {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  object-fit: contain;
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(202deg) brightness(102%) contrast(97%);
  transition: all 0.3s ease;
}
.card-icon:hover .card-icon__img img {
  filter: brightness(0) saturate(100%) invert(47%) sepia(99%) saturate(2174%) hue-rotate(184deg) brightness(98%) contrast(98%);
  transform: scale(1.1);
}
.card-icon__text h3 {
  color: #333333;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  text-transform: capitalize;
  font-weight: 600;
}
.card-icon__text p {
  color: #777777;
  line-height: 1.6;
  margin-bottom: 0;
}

.doctors {
  padding: 4rem 0;
  background-color: #ffffff;
}
@media (max-width: 575px) {
  .doctors {
    padding: 3rem 0;
  }
}
.doctors__grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
}
@media (max-width: 575px) {
  .doctors__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
@media (min-width: 768px) {
  .doctors__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 992px) {
  .doctors__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.doctor-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  transition: all 0.3s ease;
  position: relative;
}
.doctor-card:hover {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  transform: translateY(-5px);
}
.doctor-card:hover {
  transform: translateY(-5px);
}
.doctor-card__image {
  position: relative;
  overflow: hidden;
  height: 250px;
}
.doctor-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}
.doctor-card__image:hover img {
  transform: scale(1.05);
}
.doctor-card__content {
  padding: 1.5rem 1rem;
  text-align: center;
  position: relative;
}
.doctor-card__specialty {
  display: inline-block;
  background-color: #2d529f;
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
  margin-bottom: 0.5rem;
  text-decoration: none;
  transition: all 0.15s ease;
}
.doctor-card__specialty:hover {
  background-color: #008fe2;
}
.doctor-card__name {
  color: #333333;
  font-size: 1.125rem;
  margin-bottom: 1rem;
  font-weight: 600;
}
.doctor-card__link {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  width: 35px;
  height: 35px;
  background-color: #ffffff;
  color: #2d529f;
  border: 2px solid #2d529f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-decoration: none;
}
.doctor-card__link:hover {
  background-color: #2d529f;
  color: #ffffff;
  transform: scale(1.1);
}
.doctor-card__link i {
  font-size: 0.875rem;
}

.resources {
  padding: 4rem 0;
  background-color: #f7f7f7;
}
@media (max-width: 575px) {
  .resources {
    padding: 3rem 0;
  }
}
.resources__grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
  margin-bottom: 3rem;
}
@media (max-width: 575px) {
  .resources__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
@media (min-width: 768px) {
  .resources__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
.resources__btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.resource-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.resource-card:hover {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  transform: translateY(-5px);
}
.resource-card:hover {
  transform: translateY(-5px);
}
.resource-card__image {
  position: relative;
  overflow: hidden;
  height: 200px;
}
.resource-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}
.resource-card__image:hover img {
  transform: scale(1.05);
}
.resource-card__content {
  padding: 1.5rem;
}
.resource-card__content h3 {
  color: #333333;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  font-weight: 600;
}
.resource-card__content p {
  color: #777777;
  line-height: 1.6;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.insurance {
  padding: 4rem 0;
  background-color: #ffffff;
}
@media (max-width: 575px) {
  .insurance {
    padding: 3rem 0;
  }
}
.insurance__logos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 3rem;
  align-items: center;
}
@media (max-width: 575px) {
  .insurance__logos {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}
.insurance__logo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}
.insurance__logo:hover {
  background-color: #f8f9fa;
  transform: translateY(-5px);
}
.insurance__logo img {
  max-width: 120px;
  max-height: 60px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: all 0.3s ease;
}
.insurance__logo:hover img {
  filter: grayscale(0%);
}

.testimonials {
  padding: 4rem 0;
  background-color: #f7f7f7;
  position: relative;
}
@media (max-width: 575px) {
  .testimonials {
    padding: 3rem 0;
  }
}
.testimonials__slider {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.testimonial {
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}
.testimonial.active {
  opacity: 1;
  visibility: visible;
}
.testimonial blockquote {
  background-color: #ffffff;
  padding: 4rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  position: relative;
  margin-bottom: 1.5rem;
}
.testimonial blockquote::before {
  content: '"';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 4rem;
  color: #2d529f;
  font-weight: 700;
  line-height: 1;
}
.testimonial blockquote p {
  font-size: 1.125rem;
  line-height: 1.8;
  color: #777777;
  font-style: italic;
  margin-bottom: 1.5rem;
}
.testimonial blockquote cite {
  color: #333333;
  font-size: 1.125rem;
  font-weight: 600;
  font-style: normal;
}
.testimonial blockquote cite::before {
  content: "— ";
  color: #2d529f;
}

.footer {
  background-color: #212529;
  color: #ffffff;
  padding: 4rem 0 1.5rem;
}
.footer__content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}
@media (max-width: 575px) {
  .footer__content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    text-align: center;
  }
}
.footer__section h3 {
  color: #ffffff;
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}
.footer__section address {
  font-style: normal;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.8);
}
.footer__section .hours p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}
.footer .emergency-number {
  color: #008fe2;
  font-size: 1.25rem;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.15s ease;
}
.footer .emergency-number:hover {
  color: #2d529f;
}
.footer .social-links {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}
@media (max-width: 575px) {
  .footer .social-links {
    justify-content: center;
  }
}
.footer .social-links a {
  width: 40px;
  height: 40px;
  background-color: #2d529f;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-decoration: none;
}
.footer .social-links a:hover {
  background-color: #008fe2;
  transform: translateY(-3px);
}
.footer .social-links a i {
  font-size: 1rem;
}
.footer__bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 1.5rem;
  text-align: center;
}
.footer__bottom p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0;
  font-size: 0.875rem;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
  text-align: center;
  font-size: 1rem;
}
.btn--primary {
  background-color: #2d529f;
  color: #ffffff;
  border: none;
  padding: 12px 30px;
  border-radius: 0.375rem;
  text-decoration: none;
  display: inline-block;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  cursor: pointer;
}
.btn--primary:hover {
  background-color: rgb(33.75, 61.5, 119.25);
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.btn--primary:active {
  transform: translateY(0);
}
.btn--secondary {
  background-color: #008fe2;
  color: #ffffff;
  border: none;
  padding: 12px 30px;
  border-radius: 0.375rem;
  text-decoration: none;
  display: inline-block;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  cursor: pointer;
}
.btn--secondary:hover {
  background-color: rgb(0, 110.7300884956, 175);
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.btn--secondary:active {
  transform: translateY(0);
}
.btn--dark {
  background-color: #212529;
  color: #ffffff;
  border: none;
  padding: 12px 30px;
  border-radius: 0.375rem;
  text-decoration: none;
  display: inline-block;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  cursor: pointer;
}
.btn--dark:hover {
  background-color: rgb(10.2567567568, 11.5, 12.7432432432);
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.btn--dark:active {
  transform: translateY(0);
}
.btn--outline-primary {
  background-color: transparent;
  color: #2d529f;
  border-color: #2d529f;
}
.btn--outline-primary:hover {
  background-color: #2d529f;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.btn--outline-secondary {
  background-color: transparent;
  color: #008fe2;
  border-color: #008fe2;
}
.btn--outline-secondary:hover {
  background-color: #008fe2;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.btn--sm {
  padding: 8px 20px;
  font-size: 0.875rem;
}
.btn--lg {
  padding: 16px 40px;
  font-size: 1.125rem;
}
.btn:disabled, .btn--disabled {
  background-color: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
.btn:disabled:hover, .btn--disabled:hover {
  background-color: #e9ecef;
  color: #6c757d;
  transform: none;
  box-shadow: none;
}

.sec-title__header {
  color: #212529;
  font-family: "Arial", "Helvetica", sans-serif;
  font-size: 1.7rem;
  margin-bottom: 10px;
  font-weight: 700;
  text-transform: capitalize;
}
.sec-title p {
  color: #777777;
}

.card-icon {
  display: flex;
  gap: 1.2rem;
}
.card-icon__text h3 {
  color: #008fe2;
  text-transform: capitalize;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}
.card-icon__text p {
  color: #777777;
}
